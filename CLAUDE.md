# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Chinese exam preparation platform built with Next.js 14, focusing on postgraduate entrance exam (考研) materials, especially English exam analysis. The system provides AI-powered sentence analysis, translation, vocabulary learning, and resource downloads for various Chinese standardized tests.

## Development Commands

- `npm run dev` - Start development server at http://localhost:3000
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run lint` - Run ESLint for code quality checks

## Architecture & Tech Stack

### Core Technologies

- **Frontend**: Next.js 14 with App Router, TypeScript, TailwindCSS
- **Database**: Supabase (migrated from MySQL)
- **AI Integration**: DeepSeek API for text analysis using OpenAI-compatible interface
- **State Management**: React hooks, Server Components by default
- **Styling**: Tailwind CSS with custom component library

### Database Architecture

The project is transitioning from MySQL to Supabase:

- Legacy MySQL connection pool exists in `lib/db.ts` for backward compatibility
- New Supabase client setup in `lib/supabase/server.ts`
- Type definitions in `types/supabase.ts`

### Key Directory Structure

- `app/` - Next.js 14 App Router pages and layouts
- `app/api/` - API routes for server-side logic
- `app/components/` - Reusable UI components
- `components/` - Legacy component directory (being migrated)
- `lib/` - Utility functions and client configurations
- `types/` - TypeScript type definitions
- `utils/` - Helper utilities and Supabase configurations

## Component Architecture

### Component Patterns

Follow the established patterns in `.cursorrules`:

- Use Server Components by default, Client Components only when needed
- Component definition: `const ComponentName = () => { ... }`
- Props interfaces: `interface ComponentNameProps { ... }`
- Use named exports for components, default exports for pages
- Avoid explicit return type annotations, let TypeScript infer

### AI Integration

- DeepSeek API integration through OpenAI SDK compatibility
- Analysis endpoints in `app/api/analyze/route.ts`
- Service layer in `app/services/deepseek.ts`
- Environment variable: `DEEPSEEK_API_KEY`

## Database Integration

### Supabase Setup

- Server-side client in `lib/supabase/server.ts`
- Middleware configuration in `utils/supabase/middleware.ts`
- Type-safe database operations with generated types

### Required Environment Variables

```
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY=
DEEPSEEK_API_KEY=
```

## Key Features & Business Logic

### Exam Analysis System

- Sentence-by-sentence analysis of exam papers
- AI-powered translation and grammar analysis
- Vocabulary extraction and explanation
- Exam point identification

### Resource Management

- File upload and download system
- Resource categorization by exam type
- Analytics tracking for download counts

### User Management

- Supabase Auth integration
- Role-based access (admin features)
- Subscription/payment system integration

## Development Guidelines

### Code Style

- Follow Next.js 14 App Router conventions
- Use TypeScript strict mode
- Tailwind CSS for all styling (no inline styles)
- Responsive design principles
- Server Components first, Client Components when necessary

### API Development

- Use route handlers in `app/api/` following App Router patterns
- Implement proper error handling and validation
- Use Supabase for database operations
- DeepSeek API for AI analysis features

### Component Development

- Create modular, reusable components
- Use proper TypeScript interfaces for props
- Implement loading and error states
- Follow accessibility best practices with ARIA attributes

## Testing & Quality

- Run `npm run lint` before commits
- No specific test framework configured (check for test files before assuming testing approach)
- Use TypeScript strict mode for type safety

## Deployment Notes

- Configured for containerized deployment (Docker references in README)
- Environment variables must be properly configured
- Supabase migration from MySQL may require data transfer considerations

# 请始终使用中文回复我的问题
