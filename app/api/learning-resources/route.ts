import { createClient } from "@supabase/supabase-js";
import { NextResponse } from "next/server";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// 资源接口定义
export interface Resource {
  id: string;
  name: string;
  size: number;
  url: string;
  created_at: string;
  type: string;
  category?: string; // 资源分类: 考研英语一/考研英语二/四级/六级/考研数学一/考研数学二/考研数学三/考研政治/考研计算机408/考研管综199
  section?: string; // 资源部分: 真题/答案解析/公式/核心词汇/手译本/经典长难句50句
}

// 通过文件名获取文件类型
function getFileTypeFromName(fileName: string) {
  const extension = fileName.split(".").pop()?.toLowerCase();
  if (extension === "pdf") return "application/pdf";
  if (extension === "doc" || extension === "docx") return "application/msword";
  if (extension === "xls" || extension === "xlsx") return "application/excel";
  if (extension === "ppt" || extension === "pptx")
    return "application/powerpoint";
  return "application/octet-stream";
}

// 获取资源函数
async function getResources(path: string, category: string, section: string) {
  const { data, error } = await supabase.storage.from("mbdata").list(path, {
    sortBy: { column: "name", order: "desc" },
  });

  if (error) {
    console.error(`获取${path}资源失败:`, error);
    return [];
  }

  if (!data || data.length === 0) {
    return [];
  }

  return await Promise.all(
    data.map(async (file) => {
      const { data: urlData } = supabase.storage
        .from("mbdata")
        .getPublicUrl(`${path}/${file.name}`);

      return {
        id: file.id,
        name: file.name,
        size: file.metadata.size,
        url: urlData.publicUrl,
        created_at: file.created_at,
        type: file.metadata.mimetype || getFileTypeFromName(file.name),
        category: category,
        section: section,
      };
    })
  );
}

// GET请求处理函数 - 获取所有学习资源
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const resourceType = searchParams.get("type") || "kaoyan1"; // 默认使用考研英语一作为默认类型
    const section = searchParams.get("section"); // 新增：获取section参数

    let resources: Resource[] = [];

    // 获取常规学习资源
    if (resourceType === "general") {
      const generalResources = await getResources("resources", "通用", "资料");
      resources = [...resources, ...generalResources];
    }

    // 获取考研英语一资源
    if (resourceType === "kaoyan1") {
      if (!section || section === "真题") {
        const kaoyan1Exams = await getResources(
          "kaoyan/English1/pastExamPapers",
          "考研英语一",
          "真题"
        );
        resources = [...resources, ...kaoyan1Exams];
      }
      if (!section || section === "答案解析") {
        const kaoyan1Explanations = await getResources(
          "kaoyan/English1/Explanation",
          "考研英语一",
          "答案解析"
        );
        resources = [...resources, ...kaoyan1Explanations];
      }
      if (!section || section === "核心词汇") {
        const kaoyan1CoreWords = await getResources(
          "kaoyan/English1/coreVocabulary",
          "考研英语一",
          "核心词汇"
        );
        resources = [...resources, ...kaoyan1CoreWords];
      }
      if (!section || section === "手译本") {
        const kaoyan1HandTranslations = await getResources(
          "kaoyan/English1/handTranslation",
          "考研英语一",
          "手译本"
        );
        resources = [...resources, ...kaoyan1HandTranslations];
      }
      if (!section || section === "经典长难句50句") {
        const kaoyan1LongSentences = await getResources(
          "kaoyan/English1/classicLongSentences",
          "考研英语一",
          "经典长难句50句"
        );
        resources = [...resources, ...kaoyan1LongSentences];
      }
    }

    // 获取考研英语二资源
    if (resourceType === "kaoyan2") {
      if (!section || section === "真题") {
        const kaoyan2Exams = await getResources(
          "kaoyan/English2/pastExamPapers",
          "考研英语二",
          "真题"
        );
        resources = [...resources, ...kaoyan2Exams];
      }
      if (!section || section === "答案解析") {
        const kaoyan2Explanations = await getResources(
          "kaoyan/English2/Explanation",
          "考研英语二",
          "答案解析"
        );
        resources = [...resources, ...kaoyan2Explanations];
      }
      if (!section || section === "核心词汇") {
        const kaoyan2CoreWords = await getResources(
          "kaoyan/English2/coreVocabulary",
          "考研英语二",
          "核心词汇"
        );
        resources = [...resources, ...kaoyan2CoreWords];
      }
      if (!section || section === "手译本") {
        const kaoyan2HandTranslations = await getResources(
          "kaoyan/English2/handTranslation",
          "考研英语二",
          "手译本"
        );
        resources = [...resources, ...kaoyan2HandTranslations];
      }
      if (!section || section === "经典长难句50句") {
        const kaoyan2LongSentences = await getResources(
          "kaoyan/English2/classicLongSentences",
          "考研英语二",
          "经典长难句50句"
        );
        resources = [...resources, ...kaoyan2LongSentences];
      }
    }

    // 获取四级资源
    if (resourceType === "cet4") {
      if (!section || section === "真题") {
        const cet4Exams = await getResources(
          "cet46/cet4/pastExamPapers",
          "四级",
          "真题"
        );
        resources = [...resources, ...cet4Exams];
      }
      if (!section || section === "答案解析") {
        const cet4Explanations = await getResources(
          "cet46/cet4/Explanation",
          "四级",
          "答案解析"
        );
        resources = [...resources, ...cet4Explanations];
      }
      if (!section || section === "核心词汇") {
        const cet4CoreWords = await getResources(
          "cet46/cet4/core1500words",
          "四级",
          "核心词汇"
        );
        resources = [...resources, ...cet4CoreWords];
      }
      if (!section || section === "音频") {
        const cet4Audio = await getResources(
          "cet46/cet4/audio",
          "四级",
          "音频"
        );
        resources = [...resources, ...cet4Audio];
      }
      if (!section || section === "25年六月四级押题") {
        const cet4Predictions = await getResources(
          "cet46/cet4/june2025predictions",
          "四级",
          "25年六月四级押题"
        );
        resources = [...resources, ...cet4Predictions];
      }
      if (!section || section === "四级分类作文模板") {
        const cet4WritingTemplates = await getResources(
          "cet46/cet4/writingTemplates",
          "四级",
          "四级分类作文模板"
        );
        resources = [...resources, ...cet4WritingTemplates];
      }
      if (!section || section === "四级作文预测") {
        const cet4WritingPredictions = await getResources(
          "cet46/cet4/writingPredictions",
          "四级",
          "四级作文预测"
        );
        resources = [...resources, ...cet4WritingPredictions];
      }
      if (!section || section === "翻译句式及模板") {
        const cet4TranslationTemplates = await getResources(
          "cet46/cet4/translationTemplates",
          "四级",
          "翻译句式及模板"
        );
        resources = [...resources, ...cet4TranslationTemplates];
      }
    }

    // 获取六级资源
    if (resourceType === "cet6") {
      if (!section || section === "真题") {
        const cet6Exams = await getResources(
          "cet46/cet6/pastExamPapers",
          "六级",
          "真题"
        );
        resources = [...resources, ...cet6Exams];
      }
      if (!section || section === "答案解析") {
        const cet6Explanations = await getResources(
          "cet46/cet6/Explanation",
          "六级",
          "答案解析"
        );
        resources = [...resources, ...cet6Explanations];
      }
      if (!section || section === "核心词汇") {
        const cet6CoreWords = await getResources(
          "cet46/cet6/core1500words",
          "六级",
          "核心词汇"
        );
        resources = [...resources, ...cet6CoreWords];
      }
      if (!section || section === "音频") {
        const cet6Audio = await getResources(
          "cet46/cet6/audio",
          "六级",
          "音频"
        );
        resources = [...resources, ...cet6Audio];
      }
      if (!section || section === "25年六月六级押题") {
        const cet6Predictions = await getResources(
          "cet46/cet6/june2025predictions",
          "六级",
          "25年六月六级押题"
        );
        resources = [...resources, ...cet6Predictions];
      }
      if (!section || section === "六级分类作文模板") {
        const cet6WritingTemplates = await getResources(
          "cet46/cet6/writingTemplates",
          "六级",
          "六级分类作文模板"
        );
        resources = [...resources, ...cet6WritingTemplates];
      }
      if (!section || section === "六级作文预测") {
        const cet6WritingPredictions = await getResources(
          "cet46/cet6/writingPredictions",
          "六级",
          "六级作文预测"
        );
        resources = [...resources, ...cet6WritingPredictions];
      }
      if (!section || section === "翻译句式及模板") {
        const cet6TranslationTemplates = await getResources(
          "cet46/cet6/translationTemplates",
          "六级",
          "翻译句式及模板"
        );
        resources = [...resources, ...cet6TranslationTemplates];
      }
    }

    // 获取考研数学一资源
    if (resourceType === "kaoyan_math1") {
      if (!section || section === "真题") {
        const math1Exams = await getResources(
          "kaoyan/Math1/pastExamPapers",
          "考研数学一",
          "真题"
        );
        resources = [...resources, ...math1Exams];
      }
      if (!section || section === "答案解析") {
        const math1Explanations = await getResources(
          "kaoyan/Math1/Explanation",
          "考研数学一",
          "答案解析"
        );
        resources = [...resources, ...math1Explanations];
      }
      if (!section || section === "公式") {
        const math1Formula = await getResources(
          "kaoyan/Math1/formula",
          "考研数学一",
          "公式"
        );
        resources = [...resources, ...math1Formula];
      }
    }

    // 获取考研数学二资源
    if (resourceType === "kaoyan_math2") {
      if (!section || section === "真题") {
        const math2Exams = await getResources(
          "kaoyan/Math2/pastExamPapers",
          "考研数学二",
          "真题"
        );
        resources = [...resources, ...math2Exams];
      }
      if (!section || section === "答案解析") {
        const math2Explanations = await getResources(
          "kaoyan/Math2/Explanation",
          "考研数学二",
          "答案解析"
        );
        resources = [...resources, ...math2Explanations];
      }
      if (!section || section === "公式") {
        const math2Formula = await getResources(
          "kaoyan/Math1/formula",
          "考研数学二",
          "公式"
        );
        resources = [...resources, ...math2Formula];
      }
    }

    // 获取考研数学三资源
    if (resourceType === "kaoyan_math3") {
      if (!section || section === "真题") {
        const math3Exams = await getResources(
          "kaoyan/Math3/pastExamPapers",
          "考研数学三",
          "真题"
        );
        resources = [...resources, ...math3Exams];
      }
      if (!section || section === "答案解析") {
        const math3Explanations = await getResources(
          "kaoyan/Math3/Explanation",
          "考研数学三",
          "答案解析"
        );
        resources = [...resources, ...math3Explanations];
      }
      if (!section || section === "公式") {
        const math3Formula = await getResources(
          "kaoyan/Math1/formula",
          "考研数学三",
          "公式"
        );
        resources = [...resources, ...math3Formula];
      }
    }

    // 获取考研政治资源
    if (resourceType === "kaoyan_politics") {
      if (!section || section === "真题") {
        const politicsExams = await getResources(
          "kaoyan/Politics/pastExamPapers",
          "考研政治",
          "真题"
        );
        resources = [...resources, ...politicsExams];
      }
      if (!section || section === "答案解析") {
        const politicsExplanations = await getResources(
          "kaoyan/Politics/Explanation",
          "考研政治",
          "答案解析"
        );
        resources = [...resources, ...politicsExplanations];
      }
    }

    // 获取考研计算机408资源
    if (resourceType === "kaoyan_408") {
      if (!section || section === "真题") {
        const computer408Exams = await getResources(
          "kaoyan/CS408/pastExamPapers",
          "考研计算机408",
          "真题"
        );
        resources = [...resources, ...computer408Exams];
      }
      if (!section || section === "答案解析") {
        const computer408Explanations = await getResources(
          "kaoyan/CS408/Explanation",
          "考研计算机408",
          "答案解析"
        );
        resources = [...resources, ...computer408Explanations];
      }
    }

    // 获取考研管综199资源
    if (resourceType === "kaoyan_199") {
      if (!section || section === "真题") {
        const management199Exams = await getResources(
          "kaoyan/Management199/pastExamPapers",
          "考研管综199",
          "真题"
        );
        resources = [...resources, ...management199Exams];
      }
      if (!section || section === "答案解析") {
        const management199Explanations = await getResources(
          "kaoyan/Management199/Explanation",
          "考研管综199",
          "答案解析"
        );
        resources = [...resources, ...management199Explanations];
      }
    }

    // 获取考研经济396资源
    if (resourceType === "kaoyan_396") {
      if (!section || section === "真题") {
        const economics396Exams = await getResources(
          "kaoyan/Economics396/pastExamPapers",
          "考研经济396",
          "真题"
        );
        resources = [...resources, ...economics396Exams];
      }
      if (!section || section === "答案解析") {
        const economics396Explanations = await getResources(
          "kaoyan/Economics396/Explanation",
          "考研经济396",
          "答案解析"
        );
        resources = [...resources, ...economics396Explanations];
      }
    }

    // 获取考研西综306资源
    if (resourceType === "kaoyan_306") {
      if (!section || section === "真题") {
        const western306Exams = await getResources(
          "kaoyan/Western306/pastExamPapers",
          "考研西综306",
          "真题"
        );
        resources = [...resources, ...western306Exams];
      }
      if (!section || section === "答案解析") {
        const western306Explanations = await getResources(
          "kaoyan/Western306/Explanation",
          "考研西综306",
          "答案解析"
        );
        resources = [...resources, ...western306Explanations];
      }
    }

    // 获取考研中综307资源
    if (resourceType === "kaoyan_307") {
      if (!section || section === "真题及解析") {
        const chinese307ExamsAndExplanations = await getResources(
          "kaoyan/Chinese307/pastExamPapersAndExplanations",
          "考研中综307",
          "真题及解析"
        );
        resources = [...resources, ...chinese307ExamsAndExplanations];
      }
    }

    // 获取考研教育学311资源
    if (resourceType === "kaoyan_311") {
      if (!section || section === "真题") {
        const education311Exams = await getResources(
          "kaoyan/Education311/pastExamPapers",
          "考研教育学311",
          "真题"
        );
        resources = [...resources, ...education311Exams];
      }
      if (!section || section === "答案解析") {
        const education311Explanations = await getResources(
          "kaoyan/Education311/Explanation",
          "考研教育学311",
          "答案解析"
        );
        resources = [...resources, ...education311Explanations];
      }
    }

    // 获取考研教育综合333资源
    if (resourceType === "kaoyan_333") {
      if (!section || section === "真题") {
        const education333Exams = await getResources(
          "kaoyan/Education333/pastExamPapers",
          "考研教育综合333",
          "真题"
        );
        resources = [...resources, ...education333Exams];
      }
    }

    // 获取考研心理学312资源
    if (resourceType === "kaoyan_312") {
      if (!section || section === "真题") {
        const psychology312Exams = await getResources(
          "kaoyan/Psychology312/pastExamPapers",
          "考研心理学312",
          "真题"
        );
        resources = [...resources, ...psychology312Exams];
      }
      if (!section || section === "答案解析") {
        const psychology312Explanations = await getResources(
          "kaoyan/Psychology312/Explanation",
          "考研心理学312",
          "答案解析"
        );
        resources = [...resources, ...psychology312Explanations];
      }
    }

    // 获取考研历史学313资源
    if (resourceType === "kaoyan_313") {
      if (!section || section === "真题及解析") {
        const history313ExamsAndExplanations = await getResources(
          "kaoyan/History313/pastExamPapersAndExplanations",
          "考研历史学313",
          "真题及解析"
        );
        resources = [...resources, ...history313ExamsAndExplanations];
      }
    }

    // 获取专四资源
    if (resourceType === "tem4") {
      if (!section || section === "真题") {
        const tem4Exams = await getResources(
          "tem/tem4/pastExamPapers",
          "专四",
          "真题"
        );
        resources = [...resources, ...tem4Exams];
      }
      if (!section || section === "答案解析") {
        const tem4Explanations = await getResources(
          "tem/tem4/Explanation",
          "专四",
          "答案解析"
        );
        resources = [...resources, ...tem4Explanations];
      }
      if (!section || section === "音频") {
        const tem4Audio = await getResources("tem/tem4/audio", "专四", "音频");
        resources = [...resources, ...tem4Audio];
      }
    }

    // 获取专八资源
    if (resourceType === "tem8") {
      if (!section || section === "真题") {
        const tem8Exams = await getResources(
          "tem/tem8/pastExamPapers",
          "专八",
          "真题"
        );
        resources = [...resources, ...tem8Exams];
      }
      if (!section || section === "答案解析") {
        const tem8Explanations = await getResources(
          "tem/tem8/Explanation",
          "专八",
          "答案解析"
        );
        resources = [...resources, ...tem8Explanations];
      }
      if (!section || section === "音频") {
        const tem8Audio = await getResources("tem/tem8/audio", "专八", "音频");
        resources = [...resources, ...tem8Audio];
      }
    }

    // 获取考研答题卡资源
    if (resourceType === "kaoyan_answer_sheet") {
      const answerSheets = await getResources(
        "kaoyan/answerSheet",
        "考研答题卡",
        "打印模板"
      );
      resources = [...resources, ...answerSheets];
    }

    // 获取学习计划表资源
    if (resourceType === "study_plan") {
      const studyPlans = await getResources(
        "studyPlan",
        "学习计划表",
        "计划模板"
      );
      resources = [...resources, ...studyPlans];
    }

    // 添加缓存控制头，防止客户端过度缓存
    return NextResponse.json(
      { resources },
      {
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      }
    );
  } catch (error) {
    console.error("获取学习资源出错:", error);
    return NextResponse.json({ error: "获取资源出错" }, { status: 500 });
  }
}
