"use client";

import React from "react";
import { Info } from "lucide-react";
import { getInnovationTypeName } from "../lib/innovation-resources";
import { SubscriptionBanner } from "../components/learning-resources/SubscriptionBanner";
import { ResourceTypeSelector } from "../components/learning-resources/ResourceTypeSelector";
import { SectionFilter } from "../components/learning-resources/SectionFilter";
import { ResourceTable } from "../components/learning-resources/ResourceTable";
import { InnovationResourceTable } from "../components/learning-resources/InnovationResourceTable";
import { useLearningResources } from "../hooks/useLearningResources";

// 资源类型定义
const resourceTypes = [
  { id: "kaoyan1", label: "考研英语一" },
  { id: "kaoyan2", label: "考研英语二" },
  { id: "cet4", label: "英语四级" },
  { id: "cet6", label: "英语六级" },
  { id: "kaoyan_math1", label: "考研数学一" },
  { id: "kaoyan_math2", label: "考研数学二" },
  { id: "kaoyan_math3", label: "考研数学三" },
  { id: "kaoyan_politics", label: "考研政治" },
  { id: "kaoyan_408", label: "考研计算机408" },
  { id: "kaoyan_199", label: "考研管综199" },
  { id: "kaoyan_396", label: "考研经济396" },
  { id: "kaoyan_306", label: "考研西综306" },
  { id: "kaoyan_307", label: "考研中综307" },
  { id: "kaoyan_311", label: "考研教育学311" },
  { id: "kaoyan_333", label: "考研教育综合333" },
  { id: "kaoyan_312", label: "考研心理学312" },
  { id: "kaoyan_313", label: "考研历史学313" },
  { id: "tem4", label: "专四" },
  { id: "tem8", label: "专八" },
  { id: "neccs", label: "大学生英语竞赛" },
  { id: "kaoyan_answer_sheet", label: "考研答题卡" },
  { id: "study_plan", label: "学习计划表" },
  { id: "general", label: "通用资料" },
  { id: "print", label: "打印五分钱/页" },
];

export default function LearningResourcesPage() {
  // 使用自定义hook管理状态
  const {
    resources,
    innovationResources,
    subscriptionInfo,
    downloadCount,
    loading,
    innovationLoading,
    subscriptionLoading,
    downloadCountLoading,
    error,
    innovationError,
    selectedType,
    selectedInnovationType,
    filterSection,
    showInnovationResources,
    setSelectedType,
    setSelectedInnovationType,
    setFilterSection,
    setShowInnovationResources,
    handleDownload,
    getFilteredResources,
    getAvailableSections,
  } = useLearningResources();

  // 常量
  const maxDownloads = 3;

  // 处理资源类型切换
  const toggleResourceType = (isInnovation: boolean) => {
    setShowInnovationResources(isInnovation);
  };

  // 处理资源类型切换
  const handleTypeChange = (type: string) => {
    setSelectedType(type);
  };

  // 处理大创资源类型切换
  const handleInnovationTypeChange = (type: string) => {
    setSelectedInnovationType(type);
  };

  // 获取当前类型的名称
  const getCurrentTypeName = () => {
    const type = resourceTypes.find((t) => t.id === selectedType);
    return type ? type.label : "资源";
  };

  return (
    <div className="bg-slate-50 py-10 min-h-screen">
      <div className="container px-2 sm:px-4 md:px-6 lg:px-8 ">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">资源库</h1>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto">
            提供考研英语、政治、数学、专业课以及四六级、专四专八和大学生创新创业等资源，助力你的考试备考与创业规划
          </p>
        </div>

        {/* 订阅状态区块 */}
        <SubscriptionBanner
          subscriptionInfo={subscriptionInfo}
          subscriptionLoading={subscriptionLoading}
          downloadCountLoading={downloadCountLoading}
          downloadCount={downloadCount}
          maxDownloads={maxDownloads}
        />

        {/* 资源类型选择器 */}
        <ResourceTypeSelector
          showInnovationResources={showInnovationResources}
          selectedType={selectedType}
          selectedInnovationType={selectedInnovationType}
          resourceTypes={resourceTypes}
          onToggleResourceType={toggleResourceType}
          onTypeChange={handleTypeChange}
          onInnovationTypeChange={handleInnovationTypeChange}
        />

        {/* 考研答题卡说明 */}
        {!showInnovationResources &&
          selectedType === "kaoyan_answer_sheet" &&
          resources.length > 0 && (
            <div className="bg-amber-50 border border-amber-200 rounded-xl shadow-md p-4 mb-8">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 text-amber-600 mt-1">
                  <Info className="w-5 h-5" />
                </div>
                <div>
                  <h3 className="font-medium text-amber-800">答题卡使用说明</h3>
                  <p className="text-sm text-amber-700 mt-1">
                    以下提供各科目考研机读答题卡PDF打印模板，可用于模拟考试练习。建议使用A3/B5纸打印，不要缩放，以保持与实际答题卡大小一致。
                  </p>
                </div>
              </div>
            </div>
          )}

        {/* 学习计划表说明 */}
        {!showInnovationResources &&
          selectedType === "study_plan" &&
          resources.length > 0 && (
            <div className="bg-green-50 border border-green-200 rounded-xl shadow-md p-4 mb-8">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 text-green-600 mt-1">
                  <Info className="w-5 h-5" />
                </div>
                <div>
                  <h3 className="font-medium text-green-800">
                    学习计划表使用说明
                  </h3>
                  <p className="text-sm text-green-700 mt-1">
                    以下提供多种学习计划表模板，包括：考研时间计划表（PDF/可编辑Word版）、艾宾浩斯遗忘曲线计划表、日/周/月复习计划表、100天早起计划等。支持个性化编辑，帮助制定科学的学习时间安排和记忆复习计划。
                  </p>
                </div>
              </div>
            </div>
          )}

        {/* 资源筛选器 */}
        {!showInnovationResources &&
          selectedType !== "general" &&
          selectedType !== "kaoyan_answer_sheet" &&
          selectedType !== "study_plan" &&
          selectedType !== "print" &&
          resources.length > 0 && (
            <SectionFilter
              selectedType={selectedType}
              filterSection={filterSection}
              onSectionChange={setFilterSection}
              getAvailableSections={getAvailableSections}
            />
          )}

        {/* 打印平台展示区域 */}
        {!showInnovationResources && selectedType === "print" && (
          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            <div className="p-6 text-center">
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <p className="text-lg text-orange-800 font-medium">
                  给大家强烈推荐这个打印平台，仅需5分钱1页，自助即可下单，同学们，快给我冲鸭
                  🚀
                </p>
              </div>
              <div className="max-w-2xl mx-auto">
                <img
                  src="/images/print.jpg"
                  alt="打印平台"
                  className="w-full h-auto rounded-lg shadow-lg mb-6"
                />
              </div>
            </div>
          </div>
        )}

        {/* 考试资源加载中或错误提示 */}
        {!showInnovationResources &&
          selectedType !== "print" &&
          (loading ? (
            <div className="text-center py-20 bg-white rounded-xl shadow-md">
              <div className="text-7xl mb-4">⏳</div>
              <h3 className="text-xl font-medium text-gray-800 mb-2">
                加载中...
              </h3>
              <p className="text-gray-600">正在获取学习资源，请稍候</p>
            </div>
          ) : error ? (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          ) : getFilteredResources().length === 0 ? (
            <div className="text-center py-20 bg-white rounded-xl shadow-md">
              <div className="text-7xl mb-4">📚</div>
              <h3 className="text-xl font-medium text-gray-800 mb-2">
                资源准备中
              </h3>
              <p className="text-gray-600">
                我们正在整理优质的{getCurrentTypeName()}学习资源，敬请期待！
              </p>
            </div>
          ) : (
            <ResourceTable
              resources={getFilteredResources()}
              onDownload={handleDownload}
            />
          ))}

        {/* 大创资源显示部分 */}
        {showInnovationResources &&
          (innovationLoading ? (
            <div className="text-center py-20 bg-white rounded-xl shadow-md">
              <div className="text-7xl mb-4">⏳</div>
              <h3 className="text-xl font-medium text-gray-800 mb-2">
                加载中...
              </h3>
              <p className="text-gray-600">正在获取创新创业资源，请稍候</p>
            </div>
          ) : innovationError ? (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {innovationError}
            </div>
          ) : innovationResources.length === 0 ? (
            <div className="text-center py-20 bg-white rounded-xl shadow-md">
              <div className="text-7xl mb-4">📚</div>
              <h3 className="text-xl font-medium text-gray-800 mb-2">
                资源准备中
              </h3>
              <p className="text-gray-600">
                我们正在整理优质的
                {getInnovationTypeName(selectedInnovationType)}
                创新创业资源，敬请期待！
              </p>
            </div>
          ) : (
            <InnovationResourceTable
              resources={innovationResources}
              onDownload={handleDownload}
            />
          ))}

        <div className="mt-16 bg-blue-50 border border-blue-100 rounded-xl p-6 sm:p-8 text-center">
          <h3 className="text-2xl font-semibold text-blue-800 mb-4">
            想要更多备考资料？
          </h3>
          <p className="text-blue-700 mb-6">
            关注公众号「面包资料屋」获取更多独家考研、四六级等考试资料和学习方法。
          </p>
          <div className="inline-flex items-center justify-center px-5 py-3 bg-blue-600 text-white rounded-lg text-lg">
            <span className="mr-2">📱</span>
            公众号：面包资料屋
          </div>
        </div>
      </div>
    </div>
  );
}
