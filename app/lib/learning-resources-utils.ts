// 学习资源相关的工具函数

// 格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// 获取用户友好的文件类型名称
export function getReadableFileType(mimeType: string): string {
  const type = mimeType.toLowerCase();

  if (type.includes("pdf")) return "PDF文档";
  if (type.includes("word") || type.includes("doc")) return "Word文档";
  if (type.includes("sheet") || type.includes("excel") || type.includes("xls"))
    return "Excel表格";
  if (
    type.includes("presentation") ||
    type.includes("powerpoint") ||
    type.includes("ppt")
  )
    return "PPT演示文稿";
  if (type.includes("image")) return "图片文件";
  if (type.includes("text")) return "文本文件";
  if (
    type.includes("audio") ||
    type.includes("mp3") ||
    type.includes("wav") ||
    type.includes("m4a") ||
    type.includes("aac")
  )
    return "音频文件";
  if (
    type.includes("zip") ||
    type.includes("rar") ||
    type.includes("compressed")
  )
    return "压缩文件";

  // 提取扩展名作为后备显示方式
  const extension = mimeType.split("/").pop()?.split(".").pop();
  if (extension && extension.length < 6)
    return extension.toUpperCase() + "文件";

  return "文档文件";
}

// 通过资源分类获取图标
export function getCategoryIcon(category: string): string {
  switch (category) {
    case "考研英语一":
      return "🎯";
    case "考研英语二":
      return "📚";
    case "考研政治":
      return "📜";
    case "考研数学一":
      return "📐";
    case "考研数学二":
      return "📏";
    case "考研数学三":
      return "🔢";
    case "考研专业课":
      return "📊";
    case "考研计算机408":
      return "💻";
    case "考研管综199":
      return "📈";
    case "考研经济396":
      return "📋";
    case "考研西综306":
      return "🩺";
    case "考研中综307":
      return "🏥";
    case "考研教育学311":
      return "🎓";
    case "考研教育综合333":
      return "📖";
    case "考研心理学312":
      return "🧠";
    case "考研答题卡":
      return "📝";
    case "学习计划表":
      return "📅";
    case "四级":
      return "🔍";
    case "六级":
      return "📝";
    case "专四":
      return "📘";
    case "专八":
      return "📗";
    case "大学生英语竞赛":
      return "🏆";
    case "通用资料":
      return "📌";
    case "考研历史学313":
      return "📜";
    default:
      return "📄";
  }
}

// 通过资源部分获取图标
export function getSectionIcon(section: string): string {
  switch (section) {
    case "真题":
      return "📄";
    case "答案解析":
      return "✏️";
    case "资料":
      return "📑";
    case "核心词汇":
      return "📚";
    case "公式":
      return "📊";
    case "打印模板":
      return "🖨️";
    case "手译本":
      return "✍️";
    case "计划模板":
      return "📅";
    case "音频":
      return "🎵";
    case "25年六月四级押题":
      return "🎯";
    case "25年六月六级押题":
      return "🎯";
    case "四级分类作文模板":
      return "📝";
    case "六级分类作文模板":
      return "📝";
    case "四级作文预测":
      return "🔮";
    case "六级作文预测":
      return "🔮";
    case "翻译句式及模板":
      return "🔄";
    case "经典长难句50句":
      return "📖";
    default:
      return "📋";
  }
}

// 获取分类样式
export function getCategoryStyles(category: string): string {
  switch (category) {
    case "考研英语一":
      return "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100";
    case "考研英语二":
      return "bg-indigo-50 text-indigo-600 border border-indigo-100 hover:bg-indigo-100";
    case "考研政治":
      return "bg-red-50 text-red-600 border border-red-100 hover:bg-red-100";
    case "考研数学一":
      return "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100";
    case "考研数学二":
      return "bg-fuchsia-50 text-fuchsia-600 border border-fuchsia-100 hover:bg-fuchsia-100";
    case "考研数学三":
      return "bg-violet-50 text-violet-600 border border-violet-100 hover:bg-violet-100";
    case "考研专业课":
      return "bg-orange-50 text-orange-600 border border-orange-100 hover:bg-orange-100";
    case "考研计算机408":
      return "bg-sky-50 text-sky-600 border border-sky-100 hover:bg-sky-100";
    case "考研管综199":
      return "bg-pink-50 text-pink-600 border border-pink-100 hover:bg-pink-100";
    case "考研经济396":
      return "bg-rose-50 text-rose-600 border border-rose-100 hover:bg-rose-100";
    case "考研西综306":
      return "bg-emerald-50 text-emerald-600 border border-emerald-100 hover:bg-emerald-100";
    case "考研中综307":
      return "bg-teal-50 text-teal-600 border border-teal-100 hover:bg-teal-100";
    case "考研教育学311":
      return "bg-slate-50 text-slate-600 border border-slate-100 hover:bg-slate-100";
    case "考研教育综合333":
      return "bg-teal-50 text-teal-600 border border-teal-100 hover:bg-teal-100";
    case "考研心理学312":
      return "bg-pink-50 text-pink-600 border border-pink-100 hover:bg-pink-100";
    case "考研历史学313":
      return "bg-stone-50 text-stone-600 border border-stone-100 hover:bg-stone-100";
    case "考研答题卡":
      return "bg-yellow-50 text-yellow-600 border border-yellow-100 hover:bg-yellow-100";
    case "学习计划表":
      return "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100";
    case "四级":
      return "bg-cyan-50 text-cyan-600 border border-cyan-100 hover:bg-cyan-100";
    case "六级":
      return "bg-teal-50 text-teal-600 border border-teal-100 hover:bg-teal-100";
    case "专四":
      return "bg-emerald-50 text-emerald-600 border border-emerald-100 hover:bg-emerald-100";
    case "专八":
      return "bg-lime-50 text-lime-600 border border-lime-100 hover:bg-lime-100";
    case "大学生英语竞赛":
      return "bg-amber-50 text-amber-600 border border-amber-100 hover:bg-amber-100";
    case "通用资料":
      return "bg-gray-50 text-gray-600 border border-gray-100 hover:bg-gray-100";
    default:
      return "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100";
  }
}

// 获取部分样式
export function getSectionStyles(section: string): string {
  switch (section) {
    case "真题":
      return "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100";
    case "答案解析":
      return "bg-yellow-50 text-yellow-700 border border-yellow-100 hover:bg-yellow-100";
    case "资料":
      return "bg-pink-50 text-pink-600 border border-pink-100 hover:bg-pink-100";
    case "核心词汇":
      return "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100";
    case "公式":
      return "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100";
    case "打印模板":
      return "bg-amber-50 text-amber-700 border border-amber-100 hover:bg-amber-100";
    case "手译本":
      return "bg-teal-50 text-teal-600 border border-teal-100 hover:bg-teal-100";
    case "计划模板":
      return "bg-emerald-50 text-emerald-600 border border-emerald-100 hover:bg-emerald-100";
    case "音频":
      return "bg-violet-50 text-violet-600 border border-violet-100 hover:bg-violet-100";
    case "25年六月四级押题":
      return "bg-red-50 text-red-600 border border-red-100 hover:bg-red-100";
    case "25年六月六级押题":
      return "bg-red-50 text-red-600 border border-red-100 hover:bg-red-100";
    case "四级分类作文模板":
      return "bg-indigo-50 text-indigo-600 border border-indigo-100 hover:bg-indigo-100";
    case "六级分类作文模板":
      return "bg-indigo-50 text-indigo-600 border border-indigo-100 hover:bg-indigo-100";
    case "四级作文预测":
      return "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100";
    case "六级作文预测":
      return "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100";
    case "翻译句式及模板":
      return "bg-cyan-50 text-cyan-600 border border-cyan-100 hover:bg-cyan-100";
    case "经典长难句50句":
      return "bg-rose-50 text-rose-600 border border-rose-100 hover:bg-rose-100";
    default:
      return "bg-gray-50 text-gray-600 border border-gray-100 hover:bg-gray-100";
  }
}

// 提取年份从文件名
export function extractYearFromFilename(filename: string): string {
  const match = filename.match(/(\d{4})/);
  return match ? match[1] : "";
}

// 按年份排序资源
export function sortResourcesByYear<T extends { name: string; section?: string }>(resources: T[]): T[] {
  return [...resources].sort((a, b) => {
    // 特殊处理四六级分类作文模板 - 按数字顺序排列
    if (
      a.section === "四级分类作文模板" ||
      a.section === "六级分类作文模板"
    ) {
      const extractNumber = (filename: string) => {
        const match = filename.match(/^(\d+)、/);
        return match ? parseInt(match[1], 10) : 0;
      };

      const numA = extractNumber(a.name);
      const numB = extractNumber(b.name);

      if (numA !== numB) {
        return numA - numB; // 升序排列：1, 2, 3...
      }
    }

    // 其他资源按年份排序
    const yearA = extractYearFromFilename(a.name);
    const yearB = extractYearFromFilename(b.name);
    return yearB.localeCompare(yearA); // 降序排列
  });
}
