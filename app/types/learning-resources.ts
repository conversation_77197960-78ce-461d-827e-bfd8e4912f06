// 学习资源相关的类型定义

export interface Resource {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  category?: string;
  section?: string;
  created_at?: string;
}

export interface InnovationResource {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  category?: string;
}

export interface SubscriptionInfo {
  isActive?: boolean;
  type?: string;
  endDate?: string;
  isSubscribed?: boolean;
  isLifetimeMember?: boolean;
}

export interface ResourceType {
  id: string;
  label: string;
}

export interface InnovationResourceType {
  id: string;
  label: string;
}

// 组件Props类型定义
export interface SubscriptionBannerProps {
  subscriptionInfo: SubscriptionInfo | null;
  subscriptionLoading: boolean;
  downloadCountLoading: boolean;
  downloadCount: number;
  maxDownloads: number;
}

export interface ResourceTypeSelectorProps {
  showInnovationResources: boolean;
  selectedType: string;
  selectedInnovationType: string;
  resourceTypes: ResourceType[];
  onToggleResourceType: (isInnovation: boolean) => void;
  onTypeChange: (type: string) => void;
  onInnovationTypeChange: (type: string) => void;
}

export interface SectionFilterProps {
  selectedType: string;
  filterSection: string | null;
  onSectionChange: (section: string) => void;
  getAvailableSections: (type: string) => string[];
}

export interface ResourceTableProps {
  resources: Resource[];
  onDownload: (resource: Resource) => void;
}

export interface InnovationResourceTableProps {
  resources: InnovationResource[];
  onDownload: (resource: InnovationResource) => void;
}

// Hook返回类型定义
export interface UseLearningResourcesReturn {
  // 状态
  resources: Resource[];
  innovationResources: InnovationResource[];
  subscriptionInfo: SubscriptionInfo | null;
  downloadCount: number;
  loading: boolean;
  innovationLoading: boolean;
  subscriptionLoading: boolean;
  downloadCountLoading: boolean;
  error: string | null;
  innovationError: string | null;

  // 筛选状态
  selectedType: string;
  selectedInnovationType: string;
  filterSection: string | null;
  showInnovationResources: boolean;

  // 操作函数
  setSelectedType: (type: string) => void;
  setSelectedInnovationType: (type: string) => void;
  setFilterSection: (section: string | null) => void;
  setShowInnovationResources: (show: boolean) => void;
  handleDownload: (resource: Resource | InnovationResource) => Promise<void>;
  getFilteredResources: () => Resource[];
  getAvailableSections: (type: string) => string[];
}

// API响应类型定义
export interface LearningResourcesApiResponse {
  resources: Resource[];
  success?: boolean;
  message?: string;
}

export interface InnovationResourcesApiResponse {
  resources: InnovationResource[];
  success?: boolean;
  message?: string;
}

export interface SubscriptionApiResponse {
  subscriptionInfo?: SubscriptionInfo;
  success?: boolean;
  data?: SubscriptionInfo;
  message?: string;
}

export interface DownloadCountApiResponse {
  currentDownloads: number;
  maxDownloads: number;
  success?: boolean;
  message?: string;
}

// 工具函数类型定义
export type FileIconFunction = (type: string) => React.ReactElement;
export type FormatFileSizeFunction = (bytes: number) => string;
export type GetReadableFileTypeFunction = (mimeType: string) => string;
export type GetCategoryIconFunction = (category: string) => string;
export type GetSectionIconFunction = (section: string) => string;
export type GetCategoryStylesFunction = (category: string) => string;
export type GetSectionStylesFunction = (section: string) => string;
export type ExtractYearFromFilenameFunction = (filename: string) => string;
export type SortResourcesByYearFunction = <
  T extends { name: string; section?: string },
>(
  resources: T[]
) => T[];
