import { useState, useEffect, useCallback } from 'react';
import { Resource } from '../api/learning-resources/route';
import { Resource as InnovationResource } from '../api/innovation-entrepreneurship/route';
import { fetchInnovationResources } from '../lib/innovation-resources';

interface SubscriptionInfo {
  isActive?: boolean;
  type?: string;
  endDate?: string;
  isSubscribed?: boolean;
  isLifetimeMember?: boolean;
}

interface UseLearningResourcesReturn {
  // 状态
  resources: Resource[];
  innovationResources: InnovationResource[];
  subscriptionInfo: SubscriptionInfo | null;
  downloadCount: number;
  loading: boolean;
  innovationLoading: boolean;
  subscriptionLoading: boolean;
  downloadCountLoading: boolean;
  error: string | null;
  innovationError: string | null;
  
  // 筛选状态
  selectedType: string;
  selectedInnovationType: string;
  filterSection: string | null;
  showInnovationResources: boolean;
  
  // 操作函数
  setSelectedType: (type: string) => void;
  setSelectedInnovationType: (type: string) => void;
  setFilterSection: (section: string | null) => void;
  setShowInnovationResources: (show: boolean) => void;
  handleDownload: (resource: Resource | InnovationResource) => Promise<void>;
  getFilteredResources: () => Resource[];
  getAvailableSections: (type: string) => string[];
}

export const useLearningResources = (): UseLearningResourcesReturn => {
  // 基础状态
  const [resources, setResources] = useState<Resource[]>([]);
  const [innovationResources, setInnovationResources] = useState<InnovationResource[]>([]);
  const [subscriptionInfo, setSubscriptionInfo] = useState<SubscriptionInfo | null>(null);
  const [downloadCount, setDownloadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [innovationLoading, setInnovationLoading] = useState(false);
  const [subscriptionLoading, setSubscriptionLoading] = useState(true);
  const [downloadCountLoading, setDownloadCountLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [innovationError, setInnovationError] = useState<string | null>(null);

  // 筛选状态
  const [selectedType, setSelectedType] = useState("kaoyan_english_1");
  const [selectedInnovationType, setSelectedInnovationType] = useState("business_plan");
  const [filterSection, setFilterSection] = useState<string | null>(null);
  const [showInnovationResources, setShowInnovationResources] = useState(false);

  // 常量
  const maxDownloads = 3;

  // 获取考试资源
  const fetchResources = useCallback(async (type: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch(`/api/learning-resources?type=${type}`);
      if (!response.ok) {
        throw new Error("获取资源失败");
      }
      const data = await response.json();
      setResources(data.resources || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取资源失败");
      setResources([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // 获取创新创业资源
  const fetchInnovationResourcesData = useCallback(async (type: string) => {
    try {
      setInnovationLoading(true);
      setInnovationError(null);
      const data = await fetchInnovationResources(type);
      setInnovationResources(data);
    } catch (err) {
      setInnovationError(err instanceof Error ? err.message : "获取创新创业资源失败");
      setInnovationResources([]);
    } finally {
      setInnovationLoading(false);
    }
  }, []);

  // 获取订阅信息
  const fetchSubscriptionInfo = useCallback(async () => {
    try {
      setSubscriptionLoading(true);
      const response = await fetch("/api/subscription-info");
      if (response.ok) {
        const data = await response.json();
        setSubscriptionInfo(data);
      } else {
        setSubscriptionInfo(null);
      }
    } catch (err) {
      setSubscriptionInfo(null);
    } finally {
      setSubscriptionLoading(false);
    }
  }, []);

  // 获取下载次数
  const fetchDownloadCount = useCallback(async () => {
    try {
      setDownloadCountLoading(true);
      const response = await fetch("/api/download-count");
      if (response.ok) {
        const data = await response.json();
        setDownloadCount(data.count || 0);
      }
    } catch (err) {
      console.error("获取下载次数失败:", err);
    } finally {
      setDownloadCountLoading(false);
    }
  }, []);

  // 处理下载
  const handleDownload = useCallback(async (resource: Resource | InnovationResource) => {
    try {
      const response = await fetch("/api/download", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          resourceId: resource.id,
          resourceName: resource.name,
          resourceType: resource.type,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "下载失败");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.style.display = "none";
      a.href = url;
      a.download = resource.name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // 更新下载次数
      await fetchDownloadCount();
    } catch (err) {
      console.error("下载失败:", err);
      alert(err instanceof Error ? err.message : "下载失败，请稍后重试");
    }
  }, [fetchDownloadCount]);

  // 获取筛选后的资源
  const getFilteredResources = useCallback((): Resource[] => {
    if (!filterSection) return resources;
    return resources.filter((resource) => resource.section === filterSection);
  }, [resources, filterSection]);

  // 获取可用的section选项
  const getAvailableSections = useCallback((type: string): string[] => {
    const typeResources = resources.filter((resource) => 
      resource.category && resource.category.includes(type.replace('_', ' '))
    );
    const sections = Array.from(new Set(typeResources.map((r) => r.section).filter(Boolean)));
    return sections as string[];
  }, [resources]);

  // 初始化数据
  useEffect(() => {
    fetchSubscriptionInfo();
    fetchDownloadCount();
  }, [fetchSubscriptionInfo, fetchDownloadCount]);

  // 当选择的类型改变时获取资源
  useEffect(() => {
    if (!showInnovationResources) {
      fetchResources(selectedType);
      setFilterSection(null);
    }
  }, [selectedType, showInnovationResources, fetchResources]);

  // 当选择的创新创业类型改变时获取资源
  useEffect(() => {
    if (showInnovationResources) {
      fetchInnovationResourcesData(selectedInnovationType);
    }
  }, [selectedInnovationType, showInnovationResources, fetchInnovationResourcesData]);

  return {
    // 状态
    resources,
    innovationResources,
    subscriptionInfo,
    downloadCount,
    loading,
    innovationLoading,
    subscriptionLoading,
    downloadCountLoading,
    error,
    innovationError,
    
    // 筛选状态
    selectedType,
    selectedInnovationType,
    filterSection,
    showInnovationResources,
    
    // 操作函数
    setSelectedType,
    setSelectedInnovationType,
    setFilterSection,
    setShowInnovationResources,
    handleDownload,
    getFilteredResources,
    getAvailableSections,
  };
};
