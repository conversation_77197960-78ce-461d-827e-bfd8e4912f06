import { useState, useEffect, useCallback } from "react";
import { Resource } from "../api/learning-resources/route";
import { Resource as InnovationResource } from "../api/innovation-entrepreneurship/route";
import { fetchInnovationResources } from "../lib/innovation-resources";
import { SubscriptionInfo } from "../types/learning-resources";

interface UseLearningResourcesReturn {
  // 状态
  resources: Resource[];
  innovationResources: InnovationResource[];
  subscriptionInfo: SubscriptionInfo | null;
  downloadCount: number;
  loading: boolean;
  innovationLoading: boolean;
  subscriptionLoading: boolean;
  downloadCountLoading: boolean;
  error: string | null;
  innovationError: string | null;

  // 筛选状态
  selectedType: string;
  selectedInnovationType: string;
  filterSection: string | null;
  showInnovationResources: boolean;

  // 操作函数
  setSelectedType: (type: string) => void;
  setSelectedInnovationType: (type: string) => void;
  setFilterSection: (section: string | null) => void;
  setShowInnovationResources: (show: boolean) => void;
  handleDownload: (resource: Resource | InnovationResource) => Promise<void>;
  getFilteredResources: () => Resource[];
  getAvailableSections: (type: string) => string[];
}

export const useLearningResources = (): UseLearningResourcesReturn => {
  // 基础状态
  const [resources, setResources] = useState<Resource[]>([]);
  const [innovationResources, setInnovationResources] = useState<
    InnovationResource[]
  >([]);
  const [subscriptionInfo, setSubscriptionInfo] =
    useState<SubscriptionInfo | null>(null);
  const [downloadCount, setDownloadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [innovationLoading, setInnovationLoading] = useState(false);
  const [subscriptionLoading, setSubscriptionLoading] = useState(true);
  const [downloadCountLoading, setDownloadCountLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [innovationError, setInnovationError] = useState<string | null>(null);

  // 筛选状态
  const [selectedType, setSelectedType] = useState("kaoyan1");
  const [selectedInnovationType, setSelectedInnovationType] =
    useState("recommended");
  const [filterSection, setFilterSection] = useState<string | null>("真题");
  const [showInnovationResources, setShowInnovationResources] = useState(false);

  // 获取考试资源
  const fetchResources = useCallback(
    async (type: string, section?: string | null) => {
      try {
        setLoading(true);
        setError(null);

        // 添加时间戳防止缓存
        const timestamp = new Date().getTime();
        let url = `/api/learning-resources?t=${timestamp}&type=${type}`;

        // 如果有选择的section，添加到URL中
        if (section) {
          url += `&section=${encodeURIComponent(section)}`;
        }

        const response = await fetch(url, {
          cache: "no-store",
        });

        if (!response.ok) {
          throw new Error(`API请求失败: ${response.status}`);
        }

        const data = await response.json();
        setResources(data.resources || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : "获取资源失败");
        setResources([]);
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 获取创新创业资源
  const fetchInnovationResourcesData = useCallback(async (type: string) => {
    try {
      setInnovationLoading(true);
      setInnovationError(null);
      const data = await fetchInnovationResources(type);
      setInnovationResources(data);
    } catch (err) {
      setInnovationError(
        err instanceof Error ? err.message : "获取创新创业资源失败"
      );
      setInnovationResources([]);
    } finally {
      setInnovationLoading(false);
    }
  }, []);

  // 获取订阅信息
  const fetchSubscriptionInfo = useCallback(async () => {
    try {
      setSubscriptionLoading(true);

      const response = await fetch("/api/subscription");

      // 如果是401错误，表示用户未登录，不显示错误提示
      if (response.status === 401) {
        setSubscriptionInfo(null);
        return false;
      }

      if (!response.ok) {
        return false;
      }

      const data = await response.json();

      // 适配API实际返回格式
      if (data.subscriptionInfo) {
        setSubscriptionInfo(data.subscriptionInfo);
        return data.subscriptionInfo.isActive;
      } else if (data.success && data.data) {
        // 兼容旧格式
        setSubscriptionInfo(data.data);
        return data.data.isSubscribed || data.data.isLifetimeMember;
      } else {
        // 如果无法获取有效的订阅信息，默认设置为未订阅
        setSubscriptionInfo({ isActive: false });
        return false;
      }
    } catch (error) {
      // 不向用户显示错误，只在控制台记录
      console.error("获取订阅信息错误:", error);
      // 默认为未订阅状态
      setSubscriptionInfo({ isActive: false });
      return false;
    } finally {
      setSubscriptionLoading(false);
    }
  }, []);

  // 获取下载次数
  const fetchDownloadCount = useCallback(async () => {
    try {
      setDownloadCountLoading(true);
      const response = await fetch("/api/download-count");

      if (!response.ok) {
        console.error("获取下载次数失败:", response.statusText);
        return;
      }

      const data = await response.json();
      setDownloadCount(data.currentDownloads || 0);
    } catch (error) {
      console.error("获取下载次数出错:", error);
    } finally {
      setDownloadCountLoading(false);
    }
  }, []);

  // 处理下载
  const handleDownload = useCallback(
    async (resource: Resource | InnovationResource) => {
      try {
        const response = await fetch("/api/download", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            resourceId: resource.id,
            resourceName: resource.name,
            resourceType: resource.type,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "下载失败");
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = resource.name;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        // 更新下载次数
        await fetchDownloadCount();
      } catch (err) {
        console.error("下载失败:", err);
        alert(err instanceof Error ? err.message : "下载失败，请稍后重试");
      }
    },
    [fetchDownloadCount]
  );

  // 获取筛选后的资源
  const getFilteredResources = useCallback((): Resource[] => {
    if (!filterSection) return resources;
    return resources.filter((resource) => resource.section === filterSection);
  }, [resources, filterSection]);

  // 获取可用的section选项
  const getAvailableSections = useCallback((type: string): string[] => {
    switch (type) {
      case "kaoyan1":
      case "kaoyan2":
        return ["真题", "答案解析", "核心词汇", "手译本", "经典长难句50句"];
      case "cet4":
        return [
          "真题",
          "答案解析",
          "核心词汇",
          "音频",
          "25年六月四级押题",
          "四级分类作文模板",
          "四级作文预测",
          "翻译句式及模板",
        ];
      case "cet6":
        return [
          "真题",
          "答案解析",
          "核心词汇",
          "音频",
          "25年六月六级押题",
          "六级分类作文模板",
          "六级作文预测",
          "翻译句式及模板",
        ];
      case "kaoyan_math1":
      case "kaoyan_math2":
      case "kaoyan_math3":
        return ["真题", "答案解析", "公式"];
      case "kaoyan_politics":
      case "kaoyan_408":
      case "kaoyan_199":
      case "kaoyan_396":
      case "kaoyan_306":
      case "kaoyan_311":
        return ["真题", "答案解析"];
      case "kaoyan_307":
        return ["真题及解析"];
      case "kaoyan_333":
        return ["真题"];
      case "kaoyan_312":
        return ["真题", "答案解析"];
      case "kaoyan_313":
        return ["真题及解析"];
      case "tem4":
      case "tem8":
        return ["真题", "答案解析", "音频"];
      default:
        return [];
    }
  }, []);

  // 初始化数据
  useEffect(() => {
    fetchSubscriptionInfo();
    fetchDownloadCount();
  }, [fetchSubscriptionInfo, fetchDownloadCount]);

  // 当选择的类型改变时获取资源
  useEffect(() => {
    if (!showInnovationResources) {
      fetchResources(selectedType, filterSection);
    }
  }, [selectedType, filterSection, showInnovationResources, fetchResources]);

  // 当选择的创新创业类型改变时获取资源
  useEffect(() => {
    if (showInnovationResources) {
      fetchInnovationResourcesData(selectedInnovationType);
    }
  }, [
    selectedInnovationType,
    showInnovationResources,
    fetchInnovationResourcesData,
  ]);

  return {
    // 状态
    resources,
    innovationResources,
    subscriptionInfo,
    downloadCount,
    loading,
    innovationLoading,
    subscriptionLoading,
    downloadCountLoading,
    error,
    innovationError,

    // 筛选状态
    selectedType,
    selectedInnovationType,
    filterSection,
    showInnovationResources,

    // 操作函数
    setSelectedType,
    setSelectedInnovationType,
    setFilterSection,
    setShowInnovationResources,
    handleDownload,
    getFilteredResources,
    getAvailableSections,
  };
};
