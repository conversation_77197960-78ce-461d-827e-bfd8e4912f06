import React from 'react';
import { innovationResourceTypes } from '../../lib/innovation-resources';

interface ResourceType {
  id: string;
  label: string;
}

interface ResourceTypeSelectorProps {
  showInnovationResources: boolean;
  selectedType: string;
  selectedInnovationType: string;
  resourceTypes: ResourceType[];
  onToggleResourceType: (isInnovation: boolean) => void;
  onTypeChange: (type: string) => void;
  onInnovationTypeChange: (type: string) => void;
}

export const ResourceTypeSelector: React.FC<ResourceTypeSelectorProps> = ({
  showInnovationResources,
  selectedType,
  selectedInnovationType,
  resourceTypes,
  onToggleResourceType,
  onTypeChange,
  onInnovationTypeChange,
}) => {
  return (
    <div className="bg-white rounded-xl shadow-md mb-8 overflow-hidden">
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex" aria-label="Tabs">
          <button
            onClick={() => onToggleResourceType(false)}
            className={`w-1/2 py-4 px-1 text-center border-b-2 text-lg font-medium ${
              !showInnovationResources
                ? "border-blue-600 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            考试真题资源
          </button>
          <button
            onClick={() => onToggleResourceType(true)}
            className={`w-1/2 py-4 px-1 text-center border-b-2 text-lg font-medium ${
              showInnovationResources
                ? "border-blue-600 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            大学生创新创业
          </button>
        </nav>
      </div>

      {/* 子分类选择区域 */}
      <div className="p-4">
        {!showInnovationResources ? (
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-3">
              选择资源类别:
            </h3>
            <div className="flex flex-wrap gap-2">
              {resourceTypes.map((type) => (
                <button
                  key={type.id}
                  onClick={() => onTypeChange(type.id)}
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    selectedType === type.id
                      ? "bg-blue-600 text-white"
                      : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                  }`}
                >
                  {type.label}
                </button>
              ))}
            </div>
          </div>
        ) : (
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-3">
              选择创新创业资源类别:
            </h3>
            <div className="flex flex-wrap gap-2 overflow-x-auto pb-2">
              {innovationResourceTypes.map((type) => (
                <button
                  key={type.id}
                  onClick={() => onInnovationTypeChange(type.id)}
                  className={`px-4 py-2 rounded-lg transition-colors whitespace-nowrap ${
                    selectedInnovationType === type.id
                      ? "bg-blue-600 text-white"
                      : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                  }`}
                >
                  {type.label}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
