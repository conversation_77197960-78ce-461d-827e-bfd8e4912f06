import React from "react";
import { Download } from "lucide-react";
import { Resource } from "../../api/learning-resources/route";
import { getFileIcon } from "./FileIcons";
import {
  formatFileSize,
  getReadableFileType,
  getCategoryIcon,
  getSectionIcon,
  getCategoryStyles,
  getSectionStyles,
  sortResourcesByYear,
} from "../../lib/learning-resources-utils";

interface ResourceTableProps {
  resources: Resource[];
  onDownload: (resource: Resource) => void;
}

export const ResourceTable: React.FC<ResourceTableProps> = React.memo(
  ({ resources, onDownload }) => {
    const sortedResources = sortResourcesByYear(resources);

    return (
      <>
        {/* 桌面端表格视图 - 大屏幕显示 */}
        <div className="hidden md:block bg-white rounded-xl shadow-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    文件类型
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    文件名称
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    资源分类
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    文件大小
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sortedResources.map((resource) => (
                  <tr
                    key={resource.id + resource.name}
                    className="hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span>{getFileIcon(resource.type)}</span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-900">
                        {resource.name}
                      </div>
                      <div className="text-xs text-gray-500">
                        {getReadableFileType(resource.type)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-wrap gap-2">
                        {resource.category && (
                          <span
                            className={`inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium shadow-sm transition-colors ${getCategoryStyles(resource.category)}`}
                          >
                            <span className="mr-1">
                              {getCategoryIcon(resource.category)}
                            </span>{" "}
                            {resource.category}
                          </span>
                        )}
                        {resource.section && (
                          <span
                            className={`inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium shadow-sm transition-colors ${getSectionStyles(resource.section)}`}
                          >
                            <span className="mr-1">
                              {getSectionIcon(resource.section)}
                            </span>{" "}
                            {resource.section}
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium bg-gray-50 text-gray-600 border border-gray-100 shadow-sm hover:bg-gray-100 transition-colors">
                        <span className="mr-1">💾</span>{" "}
                        {formatFileSize(resource.size)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end">
                        <button
                          onClick={() => onDownload(resource)}
                          className="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          <Download className="w-4 h-4 mr-1" />
                          下载
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* 移动端列表视图 - 小屏幕显示 */}
        <div className="md:hidden bg-white rounded-xl shadow-md overflow-hidden">
          <div className="divide-y divide-gray-200">
            {sortedResources.map((resource) => (
              <div
                key={resource.id + resource.name}
                className="bg-white p-4 border-b border-gray-200 last:border-b-0"
              >
                <div className="flex items-start">
                  <div className="mr-3">{getFileIcon(resource.type)}</div>
                  <div className="flex-1 min-w-0">
                    <div className="text-base font-medium text-gray-900 truncate mb-1">
                      {resource.name}
                    </div>

                    <div className="flex flex-wrap gap-2 mb-3">
                      {resource.category && (
                        <span
                          className={`inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium shadow-sm transition-colors ${getCategoryStyles(resource.category)}`}
                        >
                          <span className="mr-1">
                            {getCategoryIcon(resource.category)}
                          </span>{" "}
                          {resource.category}
                        </span>
                      )}
                      {resource.section && (
                        <span
                          className={`inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium shadow-sm transition-colors ${getSectionStyles(resource.section)}`}
                        >
                          <span className="mr-1">
                            {getSectionIcon(resource.section)}
                          </span>{" "}
                          {resource.section}
                        </span>
                      )}
                      <span className="inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium bg-gray-50 text-gray-600 border border-gray-100 shadow-sm hover:bg-gray-100 transition-colors">
                        <span className="mr-1">💾</span>{" "}
                        {formatFileSize(resource.size)}
                      </span>
                    </div>

                    <div className="flex space-x-2">
                      <button
                        onClick={() => onDownload(resource)}
                        className="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <Download className="w-4 h-4 mr-1" />
                        下载
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </>
    );
  }
);
