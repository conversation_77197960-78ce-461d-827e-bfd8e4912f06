import React from 'react';
import {
  FileText,
  FileSpreadsheet,
  FileImage,
  File,
} from "lucide-react";

// PDF文件图标
export const PdfIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="text-red-500"
  >
    <path
      d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="#FEEFEE"
    />
    <path
      d="M14 2V8H20"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path d="M11.5 12.5H12.5V16.5H11.5V12.5Z" fill="currentColor" />
    <path
      d="M9 13C9 12.7239 9.22386 12.5 9.5 12.5H10.5C10.7761 12.5 11 12.7239 11 13V16C11 16.2761 10.7761 16.5 10.5 16.5H9.5C9.22386 16.5 9 16.2761 9 16V13Z"
      fill="currentColor"
    />
    <path
      d="M13 13C13 12.7239 13.2239 12.5 13.5 12.5H14.5C14.7761 12.5 15 12.7239 15 13V16C15 16.2761 14.7761 16.5 14.5 16.5H13.5C13.2239 16.5 13 16.2761 13 16V13Z"
      fill="currentColor"
    />
    <path
      d="M7 16.5H17"
      stroke="currentColor"
      strokeWidth="1"
      strokeLinecap="round"
    />
  </svg>
);

// Word文档图标
export const DocIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="text-blue-600"
  >
    <path
      d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="#EFF6FF"
    />
    <path
      d="M14 2V8H20"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect x="8" y="14" width="8" height="1.5" rx="0.75" fill="currentColor" />
    <rect x="8" y="11" width="8" height="1.5" rx="0.75" fill="currentColor" />
    <rect x="8" y="17" width="8" height="1.5" rx="0.75" fill="currentColor" />
  </svg>
);

// Excel表格图标
export const XlsIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="text-green-600"
  >
    <path
      d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="#ECFDF5"
    />
    <path
      d="M14 2V8H20"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect
      x="6"
      y="13"
      width="12"
      height="7"
      rx="1"
      fill="currentColor"
      fillOpacity="0.2"
    />
    <path d="M12 13V20" stroke="currentColor" strokeLinecap="round" />
    <path d="M6 16.5H18" stroke="currentColor" strokeLinecap="round" />
  </svg>
);

// PowerPoint演示文稿图标
export const PptIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="text-orange-500"
  >
    <path
      d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="#FFF7ED"
    />
    <path
      d="M14 2V8H20"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect
      x="8"
      y="12"
      width="8"
      height="6"
      rx="1"
      fill="currentColor"
      fillOpacity="0.2"
    />
    <circle cx="12" cy="10" r="2" fill="currentColor" fillOpacity="0.5" />
  </svg>
);

// 音频文件图标
export const AudioIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="text-purple-600"
  >
    <path
      d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="#F3E8FF"
    />
    <path
      d="M14 2V8H20"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle cx="12" cy="13" r="2" fill="currentColor" />
    <path
      d="M12 11V13L14 12.5"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8 15.5C8.5 16 9.5 16.5 10.5 16.5C11.5 16.5 12.5 16 13 15.5"
      stroke="currentColor"
      strokeWidth="1"
      strokeLinecap="round"
    />
  </svg>
);

// 通过文件类型获取对应的图标组件
export function getFileIcon(type: string): React.ReactElement {
  if (type.includes("pdf")) return <PdfIcon />;
  if (type.includes("word") || type.includes("doc")) return <DocIcon />;
  if (type.includes("excel") || type.includes("sheet") || type.includes("xls"))
    return <XlsIcon />;
  if (
    type.includes("powerpoint") ||
    type.includes("presentation") ||
    type.includes("ppt")
  )
    return <PptIcon />;
  if (type.includes("image"))
    return <FileImage className="w-6 h-6 text-gray-500" />;
  if (type.includes("text"))
    return <FileText className="w-6 h-6 text-gray-500" />;
  if (
    type.includes("audio") ||
    type.includes("mp3") ||
    type.includes("wav") ||
    type.includes("m4a") ||
    type.includes("aac")
  )
    return <AudioIcon />;
  if (
    type.includes("zip") ||
    type.includes("rar") ||
    type.includes("compressed")
  )
    return <File className="w-6 h-6 text-gray-500" />;

  // 提取扩展名作为后备显示方式
  const extension = type.split("/").pop()?.split(".").pop();
  if (extension && extension.length < 6)
    return <File className="w-6 h-6 text-gray-500" />;

  return <File className="w-6 h-6 text-gray-500" />;
}
