import React from 'react';

interface SectionFilterProps {
  selectedType: string;
  filterSection: string | null;
  onSectionChange: (section: string) => void;
  getAvailableSections: (type: string) => string[];
}

export const SectionFilter: React.FC<SectionFilterProps> = ({
  selectedType,
  filterSection,
  onSectionChange,
  getAvailableSections,
}) => {
  const availableSections = getAvailableSections(selectedType);

  if (availableSections.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-xl shadow-md p-4 mb-8">
      <div className="flex items-center mb-2">
        <span className="text-gray-700 font-medium mr-4">
          资源筛选:
        </span>
        <div className="flex flex-wrap gap-2">
          {availableSections.map((section) => (
            <button
              key={section}
              onClick={() => onSectionChange(section)}
              className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                filterSection === section
                  ? "bg-blue-600 text-white"
                  : "bg-gray-100 text-gray-800 hover:bg-gray-200"
              }`}
            >
              {section}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};
