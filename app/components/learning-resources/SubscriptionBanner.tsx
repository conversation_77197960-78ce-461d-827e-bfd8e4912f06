import React from 'react';
import Link from 'next/link';
import { CheckCircle, Info, LogIn, Loader2 } from 'lucide-react';

interface SubscriptionInfo {
  isActive?: boolean;
  type?: string;
  endDate?: string;
  isSubscribed?: boolean;
  isLifetimeMember?: boolean;
}

interface SubscriptionBannerProps {
  subscriptionInfo: SubscriptionInfo | null;
  subscriptionLoading: boolean;
  downloadCountLoading: boolean;
  downloadCount: number;
  maxDownloads: number;
}

export const SubscriptionBanner: React.FC<SubscriptionBannerProps> = ({
  subscriptionInfo,
  subscriptionLoading,
  downloadCountLoading,
  downloadCount,
  maxDownloads,
}) => {
  if (subscriptionLoading || downloadCountLoading) {
    return (
      <div className="bg-gray-50 p-4 rounded-lg flex items-center justify-center">
        <Loader2 className="w-5 h-5 text-blue-600 animate-spin mr-2" />
        <span className="text-gray-600">正在加载信息...</span>
      </div>
    );
  }

  // 有订阅信息且是订阅用户或终身会员
  if (
    subscriptionInfo?.isSubscribed ||
    subscriptionInfo?.isLifetimeMember ||
    subscriptionInfo?.isActive
  ) {
    const isLifetimeMember =
      subscriptionInfo?.isLifetimeMember ||
      subscriptionInfo?.type === "终身会员" ||
      subscriptionInfo?.endDate === "永久有效";

    return (
      <div className="bg-green-50 p-4 rounded-lg flex items-start gap-3">
        <div className="flex-shrink-0 text-green-600 mt-1">
          <CheckCircle className="w-5 h-5" />
        </div>
        <div>
          <h3 className="font-medium text-green-800">订阅专享内容已解锁</h3>
          <p className="text-sm text-green-700 mt-1">
            {isLifetimeMember
              ? "尊敬的终身订阅用户，您已解锁全部下载权限，可以无限制地下载所有学习资源。"
              : "作为订阅用户，您可以无限制下载所有学习资源。感谢您对我们平台的支持！"}
          </p>
        </div>
      </div>
    );
  }

  // 检查用户是否已登录（通过检查API响应判断）
  const isLoggedIn = subscriptionInfo !== null;

  // 显示下载限制提示（对所有非订阅用户显示，无论是否登录）
  return (
    <div className="bg-blue-50 p-4 rounded-lg flex items-start gap-3">
      <div className="flex-shrink-0 text-blue-600 mt-1">
        <Info className="w-5 h-5" />
      </div>
      <div>
        <h3 className="font-medium text-blue-800">下载限制提示</h3>
        <p className="text-sm text-blue-700 mt-1">
          您当前可以免费下载{maxDownloads}个学习资源，已下载
          {downloadCount}个。 订阅后可无限制下载所有资源。
        </p>
        <div className="mt-3 flex gap-2">
          {isLoggedIn ? (
            <Link
              href="/pricing"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <CheckCircle className="w-4 h-4 mr-1" />
              立即订阅
            </Link>
          ) : (
            <Link
              href="/sign-in"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <LogIn className="w-4 h-4 mr-1" />
              登录账号
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};
